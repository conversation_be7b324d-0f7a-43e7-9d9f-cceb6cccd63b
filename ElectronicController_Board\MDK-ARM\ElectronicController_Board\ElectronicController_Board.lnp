--cpu=Cortex-M4.fp
"electroniccontroller_board\startup_stm32f407xx.o"
"electroniccontroller_board\uart.o"
"electroniccontroller_board\main.o"
"electroniccontroller_board\gpio.o"
"electroniccontroller_board\dma.o"
"electroniccontroller_board\spi.o"
"electroniccontroller_board\usart.o"
"electroniccontroller_board\stm32f4xx_it.o"
"electroniccontroller_board\stm32f4xx_hal_msp.o"
"electroniccontroller_board\stm32f4xx_hal_spi.o"
"electroniccontroller_board\stm32f4xx_hal_rcc.o"
"electroniccontroller_board\stm32f4xx_hal_rcc_ex.o"
"electroniccontroller_board\stm32f4xx_hal_flash.o"
"electroniccontroller_board\stm32f4xx_hal_flash_ex.o"
"electroniccontroller_board\stm32f4xx_hal_flash_ramfunc.o"
"electroniccontroller_board\stm32f4xx_hal_gpio.o"
"electroniccontroller_board\stm32f4xx_hal_dma_ex.o"
"electroniccontroller_board\stm32f4xx_hal_dma.o"
"electroniccontroller_board\stm32f4xx_hal_pwr.o"
"electroniccontroller_board\stm32f4xx_hal_pwr_ex.o"
"electroniccontroller_board\stm32f4xx_hal_cortex.o"
"electroniccontroller_board\stm32f4xx_hal.o"
"electroniccontroller_board\stm32f4xx_hal_exti.o"
"electroniccontroller_board\stm32f4xx_hal_uart.o"
"electroniccontroller_board\system_stm32f4xx.o"
"electroniccontroller_board\delay.o"
"electroniccontroller_board\iic.o"
"electroniccontroller_board\wire.o"
"electroniccontroller_board\driver_ssd1306.o"
"electroniccontroller_board\stm32f407_driver_ssd1306_interface.o"
"electroniccontroller_board\ringbuffer.o"
"electroniccontroller_board\multitimer.o"
"electroniccontroller_board\emm_v5.o"
"electroniccontroller_board\pid.o"
"electroniccontroller_board\app_oled.o"
"electroniccontroller_board\app_motor.o"
"electroniccontroller_board\app_uasrt.o"
"electroniccontroller_board\app_maixcam.o"
"electroniccontroller_board\app_pid.o"
--library_type=microlib --strict --scatter "ElectronicController_Board\ElectronicController_Board.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "ElectronicController_Board.map" -o ElectronicController_Board\ElectronicController_Board.axf